/**
 * AnimationManager - 动画管理组件
 * 提供动画列表显示、上传、预览和控制功能
 */

'use client';

import React, { useCallback, useState } from 'react';
import * as THREE from 'three';
import { FBXLoader } from 'three/examples/jsm/loaders/FBXLoader.js';
import { useNodeSystem } from '../contexts/NodeSystemContext';
import { AnimationInfo } from '../../../../src/types/NodeTypes';

interface AnimationManagerProps {
  nodeId: string;
  animations: AnimationInfo[];
  onAnimationsUpdate: (animations: AnimationInfo[]) => void;
  className?: string;
}

export const AnimationManager: React.FC<AnimationManagerProps> = ({
  nodeId,
  animations,
  onAnimationsUpdate,
  className = ''
}) => {
  const {
    threeContext,
    isUploadingAnimation,
    animationError,
    setIsUploadingAnimation,
    setAnimationError
  } = useNodeSystem();

  const [previewingAnimationId, setPreviewingAnimationId] = useState<string | null>(null);

  // 加载动画元数据
  const loadAnimationMetadata = useCallback(async (filePath: string, fileName: string): Promise<AnimationInfo> => {
    const fbxLoader = new FBXLoader();

    return new Promise((resolve, reject) => {
      fbxLoader.load(
        filePath,
        (object: THREE.Group) => {
          if (object.animations.length === 0) {
            reject(new Error('动画文件中未找到动画数据'));
            return;
          }

          const clip = object.animations[0]; // 使用第一个动画剪辑
          const animationInfo: AnimationInfo = {
            id: `anim_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
            name: fileName.replace(/\.[^/.]+$/, ''), // 移除文件扩展名
            filePath,
            duration: clip.duration,
            tracks: clip.tracks.length,
            isDefault: animations.length === 0, // 第一个动画设为默认
            status: 'idle',
            uploadTime: new Date(),
            clip
          };

          resolve(animationInfo);
        },
        undefined,
        (error) => reject(error)
      );
    });
  }, [animations.length]);

  // 处理动画文件上传
  const handleAnimationUpload = useCallback(async (file: File) => {
    setIsUploadingAnimation(true);
    setAnimationError(null);

    try {
      // 验证文件格式
      const validExtensions = ['.fbx'];
      const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();

      if (!validExtensions.includes(fileExtension)) {
        throw new Error('动画文件仅支持 .fbx 格式');
      }

      console.log('[动画上传] 开始上传动画文件:', file.name, 'to node:', nodeId);

      // 1. 上传文件到服务器
      const formData = new FormData();
      formData.append('file', file);
      formData.append('nodeId', nodeId);
      formData.append('type', 'animation');

      const uploadResponse = await fetch('/api/upload-model', {
        method: 'POST',
        body: formData
      });

      const uploadResult = await uploadResponse.json();

      if (!uploadResult.success) {
        throw new Error(uploadResult.error || '动画文件上传失败');
      }

      console.log('[动画上传] 动画文件上传成功:', uploadResult.data.filePath);

      // 2. 加载动画文件获取元数据
      const animationInfo = await loadAnimationMetadata(uploadResult.data.filePath, file.name);

      // 3. 添加到动画列表
      const updatedAnimations = [...animations, animationInfo];
      onAnimationsUpdate(updatedAnimations);

      console.log('[动画上传] 动画添加到列表成功');

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '动画上传失败';
      setAnimationError(errorMessage);
      console.error('[动画上传] 动画上传失败:', error);
    } finally {
      setIsUploadingAnimation(false);
    }
  }, [nodeId, animations, onAnimationsUpdate, setIsUploadingAnimation, setAnimationError, loadAnimationMetadata]);

  // 预览动画
  const handlePreviewAnimation = useCallback(async (animationInfo: AnimationInfo) => {
    if (!threeContext) return;

    const { scene } = threeContext;
    const existingObject = scene.getObjectByName(nodeId);

    if (!existingObject) {
      console.warn('[动画预览] 未找到目标节点:', nodeId);
      return;
    }

    try {
      setPreviewingAnimationId(animationInfo.id);

      // 获取或创建AnimationMixer
      let mixer = existingObject.userData.animationMixer as THREE.AnimationMixer;
      if (!mixer) {
        mixer = new THREE.AnimationMixer(existingObject);
        existingObject.userData.animationMixer = mixer;
      }

      // 停止所有现有动画
      mixer.stopAllAction();

      // 播放预览动画
      if (animationInfo.clip) {
        const action = mixer.clipAction(animationInfo.clip);
        action.reset();
        action.setLoop(THREE.LoopOnce, 1);
        action.clampWhenFinished = true;
        action.play();

        // 动画结束后停止预览状态
        const onFinished = () => {
          setPreviewingAnimationId(null);
          mixer.removeEventListener('finished', onFinished);
        };
        mixer.addEventListener('finished', onFinished);
      }

      console.log('[动画预览] 开始预览动画:', animationInfo.name);

    } catch (error) {
      console.error('[动画预览] 预览失败:', error);
      setPreviewingAnimationId(null);
    }
  }, [threeContext, nodeId]);

  // 停止预览
  const handleStopPreview = useCallback(() => {
    if (!threeContext) return;

    const { scene } = threeContext;
    const existingObject = scene.getObjectByName(nodeId);

    if (existingObject) {
      const mixer = existingObject.userData.animationMixer as THREE.AnimationMixer;
      if (mixer) {
        mixer.stopAllAction();
      }
    }

    setPreviewingAnimationId(null);
  }, [threeContext, nodeId]);

  // 设置默认动画
  const handleSetDefaultAnimation = useCallback((animationId: string) => {
    const updatedAnimations = animations.map(anim => ({
      ...anim,
      isDefault: anim.id === animationId
    }));
    onAnimationsUpdate(updatedAnimations);
  }, [animations, onAnimationsUpdate]);

  // 删除动画
  const handleDeleteAnimation = useCallback((animationId: string) => {
    const updatedAnimations = animations.filter(anim => anim.id !== animationId);
    onAnimationsUpdate(updatedAnimations);
  }, [animations, onAnimationsUpdate]);

  return (
    <div className={`space-y-4 ${className}`}>
      {/* 动画上传区域 */}
      <div className="border-2 border-dashed border-gray-300 rounded-lg p-4">
        <div className="text-center">
          <div className="mb-2">
            <svg className="mx-auto h-12 w-12 text-gray-400" stroke="currentColor" fill="none" viewBox="0 0 48 48">
              <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" strokeWidth={2} strokeLinecap="round" strokeLinejoin="round" />
            </svg>
          </div>
          <div className="text-sm text-gray-600 mb-2">
            <label htmlFor={`animation-upload-${nodeId}`} className="cursor-pointer">
              <span className="text-blue-600 hover:text-blue-500">点击上传动画文件</span>
              <span className="text-gray-500"> 或拖拽文件到此处</span>
            </label>
            <input
              id={`animation-upload-${nodeId}`}
              type="file"
              className="hidden"
              accept=".fbx"
              onChange={(e) => {
                const file = e.target.files?.[0];
                if (file) {
                  handleAnimationUpload(file);
                }
              }}
              disabled={isUploadingAnimation}
            />
          </div>
          <p className="text-xs text-gray-500">支持 FBX 格式</p>
        </div>
      </div>

      {/* 上传状态 */}
      {isUploadingAnimation && (
        <div className="text-center py-2">
          <div className="inline-flex items-center text-blue-600">
            <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-blue-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            正在上传动画文件...
          </div>
        </div>
      )}

      {/* 错误信息 */}
      {animationError && (
        <div className="bg-red-50 border border-red-200 rounded-md p-3">
          <div className="flex">
            <div className="flex-shrink-0">
              <svg className="h-5 w-5 text-red-400" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
            </div>
            <div className="ml-3">
              <p className="text-sm text-red-800">{animationError}</p>
            </div>
          </div>
        </div>
      )}

      {/* 动画列表 */}
      {animations.length > 0 && (
        <div className="space-y-2">
          <h4 className="text-sm font-medium text-gray-900">动画列表</h4>
          <div className="space-y-2">
            {animations.map((animation) => (
              <div
                key={animation.id}
                className={`border rounded-lg p-3 ${
                  animation.isDefault ? 'border-blue-500 bg-blue-50' : 'border-gray-200'
                } ${previewingAnimationId === animation.id ? 'bg-yellow-50' : ''}`}
              >
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2">
                      <h5 className="text-sm font-medium text-gray-900">{animation.name}</h5>
                      {animation.isDefault && (
                        <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">
                          默认
                        </span>
                      )}
                      {previewingAnimationId === animation.id && (
                        <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-yellow-100 text-yellow-800">
                          预览中
                        </span>
                      )}
                    </div>
                    <div className="text-xs text-gray-500 mt-1">
                      时长: {animation.duration.toFixed(2)}秒 | 轨道: {animation.tracks}个
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {previewingAnimationId === animation.id ? (
                      <button
                        onClick={handleStopPreview}
                        className="text-xs bg-yellow-600 text-white px-2 py-1 rounded hover:bg-yellow-700"
                      >
                        停止
                      </button>
                    ) : (
                      <button
                        onClick={() => handlePreviewAnimation(animation)}
                        className="text-xs bg-green-600 text-white px-2 py-1 rounded hover:bg-green-700"
                      >
                        预览
                      </button>
                    )}
                    {!animation.isDefault && (
                      <button
                        onClick={() => handleSetDefaultAnimation(animation.id)}
                        className="text-xs bg-blue-600 text-white px-2 py-1 rounded hover:bg-blue-700"
                      >
                        设为默认
                      </button>
                    )}
                    <button
                      onClick={() => handleDeleteAnimation(animation.id)}
                      className="text-xs bg-red-600 text-white px-2 py-1 rounded hover:bg-red-700"
                    >
                      删除
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

export default AnimationManager;
